package wish

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 邀请同行
func InviteMember(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
		UserId uint `form:"user_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 验证心愿单是否存在并检查权限
	var wish models.Wish
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 2. 验证只有发起人可以邀请同行
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有发起人可以邀请同行")
	}

	// 3. 验证心愿单状态（只有进行中或已达成的心愿单可以邀请）
	if wish.State != constmap.WishStateProcessing && wish.State != constmap.WishStateSuccess {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前心愿单状态不可邀请同行")
	}

	// 4. 验证被邀请用户是否存在
	userMap := user_biz.LoadUsers(db, []uint{in.UserId})
	if _, exists := userMap[in.UserId]; !exists {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "被邀请用户不存在")
	}

	// 5. 验证用户是否评论过该心愿单
	var commentCount int64
	err = db.Model(&models.WishComment{}).
		Where("wish_id = ? AND user_id = ? AND state = ?",
			in.WishId, in.UserId, constmap.WishCommentStateApproved).
		Count(&commentCount).Error
	if err != nil {
		return nil, utils.NewError(err)
	}
	if commentCount == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只能邀请评论过该心愿单的用户")
	}

	// 6. 检查用户是否已经是成员或邀请状态
	var existingMember models.WishMember
	err = db.Where("wish_id = ? AND user_id = ?", in.WishId, in.UserId).
		First(&existingMember).Error

	if err == nil {
		// 用户已经是成员，检查状态
		switch existingMember.State {
		case constmap.WishMemberStateApproved:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "用户已经是心愿单成员")
		case constmap.WishMemberStateWaitReview:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "用户已申请加入，请先处理申请")
		case constmap.WishMemberStateInviteWait:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "已邀请该用户，请等待确认")
		case constmap.WishMemberStateInviteReject:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "用户已拒绝邀请，不可重复邀请")
		case constmap.WishMemberStateRejected:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "用户申请已被拒绝，不可邀请")
		case constmap.WishMemberStateKickOff:
			// 已被踢出的用户可以重新邀请，继续执行
		default:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "用户状态异常，无法邀请")
		}
	}

	// 7. 创建或更新邀请记录
	var member *models.WishMember
	if existingMember.ID > 0 && existingMember.State == constmap.WishMemberStateKickOff {
		// 更新已被踢出的成员记录
		member = &existingMember
		member.State = constmap.WishMemberStateInviteWait
		member.FollowState = constmap.WishFollowStateDefault
		if err := db.Save(member).Error; err != nil {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "邀请失败，请稍后重试")
		}
	} else {
		// 创建新的邀请记录
		member = &models.WishMember{
			WishId:      in.WishId,
			UserId:      in.UserId,
			IsOwner:     constmap.Disable,                    // 非创建者
			State:       constmap.WishMemberStateInviteWait,  // 邀请中，待确认
			FollowState: constmap.WishFollowStateDefault,
		}
		if err := db.Create(member).Error; err != nil {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "邀请失败，请稍后重试")
		}
	}

	// 8. 组装返回数据
	var out struct {
		WishId   uint `json:"wish_id"`
		UserId   uint `json:"user_id"`
		MemberId uint `json:"member_id"`
	}
	out.WishId = in.WishId
	out.UserId = in.UserId
	out.MemberId = member.ID

	return out, nil
}
